# 音频剧本生成优化架构设计（基于2M上下文与实际数据结构 - 分层处理版）

## 执行摘要

本文档基于对实际数据文件`save_witch_whole.json`的深度分析，并充分考虑利用**超长上下文能力（如Gemini 2.5 Pro 2M Token）及相关最佳实践**，提出了一个**以分层处理（概要生成后逐场景扩展）为核心、结构化数据驱动的音频剧本生成架构**。该架构首先利用LLM从完整的章节文本及结构化信息（`key_events`、`key_characters`等）中生成章节概要（场景列表及核心摘要），然后针对概要中的每个场景，在完整上下文（可缓存）的辅助下，聚焦生成详细的场景JSON。结合精准的Prompt工程（含指令末尾强调）、规则引擎验证、JSON Patch修复机制及Schema版本化，实现高质量音频剧本的高效、自动化生成，同时注重成本控制与系统韧性。

**核心原则：利用超长上下文进行全局理解，通过分层Prompt（概要-扩展）引导LLM专注处理；基于实际数据结构，通过JSON结构解析、LLM场景生成、规则引擎主导验证、LLM自我修复（Patch机制）及高级质量评估，最大限度地自动化高质量音频剧本的生成，同时严格控制成本、确保系统稳定性与可维护性。**

目标是不仅解决音频节目"谁在说话"、"情节是否吸引人"的核心挑战，更要确保系统在处理完整真实章节数据时的高效性、连贯性、可维护性和成本效益，并能优雅处理潜在的输入变更和性能瓶颈。

## 新增：小说分集与多集处理策略

长篇小说通常包含数十甚至上百个章节，直接将其作为一个整体进行处理不仅对LLM的上下文管理能力提出极高要求，也难以精细控制每一部分的节奏和情节发展。因此，引入分集处理策略至关重要。

### 1. 小说分集 (Novel Segmentation into Episodes)
   - **目标**: 将长篇小说划分为若干个逻辑单元（"集"），每集包含相对完整的情节弧线，长度适中（例如，对应原始小说中的若干章节），总集数控制在合理范围（如小于十集）。
   - **手段**:
     - **基于章节划分**: 根据总章节数量和期望的每集包含章节数，进行初步划分。
     - **基于情节推进**: 结合小说的关键情节转折点、高潮事件或自然停顿点，对基于章节的划分进行调整，确保每集的开始、发展、高潮和结尾相对完整和自然。
   - **输出**: 小说被划分为一系列的"集"，每个"集"定义了其包含的原始章节范围。

### 2. 集内独立生成 (Per-Episode Script Generation)
   - **目标**: 针对每个划分好的"集"，独立运用后续详述的"章节概要生成 -> 逐场景扩展"的完整流程，生成该集的音频剧本。
   - **手段**:
     - 当前文档描述的从"JSON结构解析与全局上下文准备"到"最终校验与高级评估与输出"的完整流程，将应用于每一"集"所包含的章节数据。
     - 在处理单集时，LLM的"全局上下文"主要聚焦于当前"集"的内容，但可选择性地注入前一集的关键摘要或结局信息，以辅助连贯性。
   - **输出**: 每个"集"独立的、经过完整处理流程生成的音频剧本。

### 3. 集间衔接与悬念设置 (Inter-Episode Linking and Cliffhangers)
   - **目标**: 确保各集之间过渡自然，并通过在集末设置悬念或伏笔来吸引听众。
   - **手段**:
     - 在指导LLM生成每集最后一个场景或结尾旁白时，可以加入特定指令，要求其考虑设置悬念或为下一集铺垫。
     - 可利用轻量级LLM分析集与集之间的过渡，识别潜在的生硬跳跃。
   - **输出**: 包含适当钩子的集末内容。

### 4. 最终集成与整体校验 (Final Integration and Holistic Review)
   - **目标**: 将所有独立生成的"集"剧本整合起来，并进行整体的结构、节奏和连贯性校验。
   - **手段**:
     - 此阶段更侧重于规则引擎和人工审核。
     - 检查各集时长、整体故事线的流畅度、角色发展是否连贯等。
     - 调整集间旁白或过渡，确保整体作品的统一性。
   - **输出**: 完整的、多集音频剧本成品。

**注：本文后续章节主要详述的是针对单一"集"（或原始设计中的"章节"）内部的剧本生成流程。分集策略作为更高层面的控制逻辑，统筹这些核心流程。**

## 基于超长上下文最佳实践与实际数据的架构设计基础

本架构充分利用超长上下文能力、已发布的最佳实践和`save_witch_whole.json`的实际数据结构，针对以下关键点进行优化：
- **分层思考与专注生成**：避免直接让LLM在2M Token中漫无目的处理，先生成高级概要（场景ID、摘要），再逐场景聚焦扩展，提升输出质量和LLM专注度。
- **整体上下文与缓存利用**：保持完整章节信息在上下文中，并明确利用上下文缓存（`context_cache_id`）机制，大幅降低重复调用（如场景扩展、修复）的成本和延迟。
- **结构化输入深度利用**：充分利用`key_events`、`key_characters`、`narrative.themes`等结构化信息指导LLM的理解和生成。
- **角色关系与行为精准追踪**：在完整上下文中，LLM能更准确地理解和再现角色关系、称呼方式及行为发展。
- **场景信息直接映射**：利用`key_events`中的`location`和`scene_description`直接指导场景生成。
- **情节连贯性与吸引力提升**：全局视角有助于LLM把握整体剧情脉络，生成更连贯、更吸引人的情节。
- **指令精准传达**：通过在Prompt末尾重复关键指令，确保LLM遵循核心要求。
- **自动化与成本控制**：通过JSON Patch自我修复、缓存等机制减少人工干预和Token消耗。
- **系统韧性与可维护性**：引入Schema版本化和优雅降级策略。

### 重要的生产环境考量与架构调整

基于实际部署经验，超长上下文虽然理论上支持2M Token，但在生产环境中存在以下限制：

#### 上下文召回质量限制
- **实际可用范围**：Gemini 2.5 Pro在超过~600k tokens后，上下文召回质量在生产延迟预算下会出现噪声
- **架构调整**：引入**检索增强上下文（RAG）**作为核心策略，而非依赖单一巨大缓存

#### 缓存稳定性风险
- **TTL限制**：默认60分钟TTL，在高负载下可能导致后续场景调用失效
- **模型升级风险**：缓存在模型版本升级时可能失效
- **架构调整**：仅对高熵的全局表（角色名册、风格指南等）使用缓存，动态上下文通过检索提供

#### 修复循环收敛性问题
- **JSON Patch震荡**：LLM更擅长重新生成小对象，而非编辑从未见过的JSON Pointer路径
- **架构调整**：采用"失败重新生成"模式，JSON Patch仅作为1-2个字段错误的备用方案

#### 新增：检索与分块的生产风险
- **固定分块粒度问题**：单个分块不当可能切断主题脉络，导致检索失效
- **架构调整**：引入**自适应语义分段**（TextTiling或基于Transformer的分段），动态调整分块边界
- **重新生成检索依赖风险**：重新生成依赖相同检索范围，可能重复相同错误
- **架构调整**：引入**负反馈检索**（"显示与X矛盾的分块"）和检索范围扩展机制

#### 新增：成本控制的长尾风险
- **场景×修正的成本爆炸**：向量查询+双重推理+修复调用可能在长尾场景下成本失控
- **架构调整**：为每集设置**LLM调用上限**（如50次/集），超出后强制降级到简化流程

#### 新增：戏剧张力盲点
- **确定性指标局限**：WPM等指标捕获节奏但无法识别戏剧冲突平淡
- **架构调整**：引入**语义张力指标**（如新引入障碍数量），使用轻量级分类模型计算

#### 新增：安全性与内容治理风险
- **RAG注入攻击**：恶意用户可在原始手稿中嵌入有害提示，检索器会将其传递给扩展模型
- **架构调整**：在索引前对分块进行**内容安全过滤**（移除HTML/脚本、过滤提示模式）
- **内容政策盲点**：原始文本包含禁止内容时缺少过滤机制
- **架构调整**：在分块索引阶段引入**内容安全扫描**，标记和隔离风险内容

## 核心理念：自适应语义分块与内容安全扫描 -> 检索增强上下文构建 -> LLM章节概要生成（双重推理） -> [LLM逐场景扩展（动态检索+影子模型验证+场景图生成） -> 脚本纯净性验证+语义张力分析 -> 负反馈检索重新生成/轻量修复] -> 人工QA检查点（明确置信度阈值） -> 双文件输出（脚本+场景图）

### 1. 自适应语义分块与内容安全扫描 (Adaptive Semantic Chunking & Content Safety Scanning)
   - **目标**: 使用自适应算法进行智能分块，确保语义完整性，同时进行安全扫描
   - **手段**: 
       - 使用TextTiling或基于Transformer的语义相似度连接进行自适应分段
       - 在索引前进行内容安全过滤：移除HTML/脚本、过滤提示注入模式
       - 对原始文本进行内容政策扫描，标记禁止内容（暴力、仇恨言论等）
       - 建立分块边界验证机制，确保关键主题不被切断
   - **输出**: 语义完整的安全分块向量索引 + 风险内容标记

### 2. 检索增强上下文构建与动态兜底机制 (RAG Context Building with Dynamic Fallback)
   - **目标**: 构建智能检索系统，支持正向检索、负反馈检索和召回质量动态兜底
   - **手段**: 
       - 基于场景概要进行标准K=5-8个语义块检索（2-3k tokens）
       - **新增-检索保真动态兜底**: 实时监测每次检索的召回质量(recall_estimate)，低于阈值(0.85)时自动触发混合检索兜底(关键词索引+模糊匹配)
       - 实现负反馈检索：当验证器指出事实X错误时，检索包含X或与X矛盾的分块
       - 检索范围动态扩展：修复失败时自动扩大检索范围，K值自适应调整(8→12)
       - 高熵全局表（角色名册、风格指南）独立缓存管理
   - **输出**: 动态上下文 + 负反馈检索能力 + 召回质量保证机制

### 3. LLM章节概要生成（双重推理保护+结构化输出） (LLM Chapter Outline Generation with Dual Inference & Structured Output)
   - **目标**: 通过双重推理提高概要稳定性，强制输出结构化角色地点信息，建立确定性的分集规则
   - **手段**: 
       - 双重推理：temperature 0.3 + 0.7 两次调用，对比差异
       - **新增-强制结构化输出**: 概要Schema强制包含main_characters(数组)和location(字符串)字段，提升后续检索精准度
       - **确定性分集规则**：单集不超过15分钟，必须在关键事件±3个场景内结束
       - 高方差场景标记为需要人工确认
       - 分集边界重现性验证：相同手稿不同随机种子应产生相同分集边界
   - **输出**: 经过双重验证的结构化章节概要列表(含角色地点信息) + 确定性分集边界

### 4. LLM逐场景扩展（动态检索+影子模型验证+场景图生成） (LLM Scene Expansion with Shadow Model & Scene Image Generation)
   - **目标**: 为每个场景提供动态检索上下文，使用影子模型验证，并生成对应的场景图描述
   - **手段**: 
       - 基于场景概要检索相关语义块，结合全局表构建上下文
       - **影子模型验证**：使用轻量级模型（如Gemini Flash）对生成场景进行期望vs实际的差异检测
       - **场景图生成**：基于word_count按120词/图规则，使用辅助Prompt生成images.json（包含index、description、approx_words）
       - **脚本纯净性保证**：强制禁止stage_dir、camera、SFX等非白名单字段，仅允许dialogue和narration
       - 显式tone/genre_style字段防止风格漂移
       - LLM调用计数器：每集最多50次LLM调用，超出后降级到简化流程
   - **输出**: 纯净脚本JSON + 独立场景图JSON + 影子模型验证报告

### 5. 脚本纯净性验证与语义张力分析 (Script Purity Validation & Semantic Tension Analysis)
   - **目标**: 确保脚本纯净性，结合确定性指标和语义张力分析，全面评估场景质量
   - **手段**: 
       - **脚本纯净性验证**：检测非白名单字段(stage_dir、camera、SFX等)，Regex检查[\[\(].*?(pan|fade|SFX|camera).*?[\]\)]模式
       - **场景图验证**：检查images数量N=ceil(total_words/120)，每张approx_words≤140，描述无对话内容
       - 确定性指标：WPM、说话人转换频率、叙述密度
       - **语义张力指标**：新引入障碍数量、冲突升级程度（使用轻量级分类模型）
       - 综合评估戏剧张力和节奏质量，避免平淡的冲突曲线
       - 内容异常检测：N-gram哈希重复检测、角色丢失检测
   - **输出**: 纯净性验证报告 + 场景图验证报告 + 多维质量指标（节奏+张力）

### 6. 负反馈检索重新生成与智能修复 (Negative Feedback Regeneration & Smart Repair)
   - **目标**: 基于验证反馈进行智能重新生成，避免重复相同错误
   - **手段**: 
       - **负反馈重新生成**：验证器指出问题时，使用负反馈检索获取矛盾或补充信息
       - 检索范围扩展：标准检索失败时自动扩大检索范围
       - JSON Patch仅用于1-2个字段的微调
       - 修复上限控制：单个场景最多修复2次，超出后标记人工审核
   - **输出**: 基于负反馈改进的场景JSON

### 7. 人工QA检查点（量化置信度公式+双文件审核） (Human QA with Quantified Confidence & Dual-File Review)
   - **目标**: 建立量化的QA触发机制和应急处理能力，确保脚本纯净性和场景图质量，使QA流程可预测、成本可控
   - **手段**: 
       - **新增-量化置信度公式**: confidence = 0.4×schema_pass_rate + 0.3×overlap_score + 0.3×(1-fp_rate)
       - **明确阈值配置**: 检索重叠度τ≥0.75, 验证器误报率ε≤0.05, 综合置信度阈值0.85
       - **QA触发条件**: confidence < 0.85 OR first_episode == True OR 纯净性验证失败, 可复现可调参
       - **双文件审核**：脚本按dialogue+narration白名单审核，场景图按"无对话"白名单审核
       - **应急预案**：向量数据库故障时回退到预生成的章节摘要
       - 内容异常监控：0.5%随机采样进行N-gram重复检测+非法字段检测
   - **输出**: 经过量化QA确认的纯净脚本 + 场景图描述文件 + 置信度评分历史 + 运营监控告警

## MVP关键短板补齐方案（P0优先级详述）

基于review提出的改进意见，以下详述3个MVP阶段必须补齐的关键短板，按P0-1 → P0-2 → P0-3顺序执行，整体工作量≈3人日。

### P0-1: 检索保真动态兜底机制

#### 背景与目标
- **问题**: 仅靠向量检索Recall@8 ≥ 90%是理想值；遇到OCR噪声或专有名词密集段落时常降至70-80%
- **风险**: 召回不足 → 场景生成缺关键信息 → 修复轮次爆增 → LLM调用失控
- **目标**: 实时监测每次检索的召回质量，低于阈值时自动补救，保证最终递交给LLM的上下文"够用"

#### 落地要点与实现

| 步骤 | 说明 |
|------|------|
| 1. 监控指标 | 在RetrievalResult中新增recall_estimate字段(0-1)，计算方式：len(hit_key_event_ids) / len(expected_key_event_ids)，实时上报Prometheus→Grafana |
| 2. 阈值&熔断 | 配置RECALL_FALLBACK_THRESHOLD = 0.85，若recall_estimate < 阈值，进入"混合检索兜底"流程 |
| 3. 混合检索兜底 | 先用关键词索引(倒排表)追加K=4结果；仍不足再用key_event.name做模糊匹配；兜底后重新计算recall，写retrieval_strategy = "HYBRID_FALLBACK" |
| 4. 指标自学习 | 连续100次检索有20次触发兜底时，将K默认从8临时提升到12，并记录事件 |
| 5. 日志记录 | 每次触发兜底记录：episode_id、scene_id、原始recall、补救策略、补救后recall |

#### 代码实现示例

```python
def get_chunks(query, expected_ids):
    res = vector_db.search(query, k=8)
    recall = calc_recall(res.ids, expected_ids)
    
    if recall < cfg.RECALL_FALLBACK_THRESHOLD:
        res = hybrid_retrieval(query, expected_ids, base=res)
        res.strategy = "HYBRID_FALLBACK"
        logger.info(f"Recall fallback triggered: {recall:.3f} → {res.final_recall:.3f}")
    
    metrics.llm_retrieval_recall.observe(recall)
    return res

def hybrid_retrieval(query, expected_ids, base):
    # 关键词索引追加
    keyword_res = keyword_index.search(query, k=4)
    # 模糊匹配补充
    fuzzy_res = fuzzy_match_key_events(expected_ids)
    # 合并去重
    final_res = merge_and_dedupe(base, keyword_res, fuzzy_res)
    final_res.final_recall = calc_recall(final_res.ids, expected_ids)
    return final_res
```

#### 验收指标
- 10部试验小说，触发兜底后Recall@8平均≥0.88
- 兜底逻辑耗时<300ms P95

### P0-2: 置信度公式量化

#### 背景与目标
- **问题**: 人工QA触发依赖"置信度分数"，但各参数(Schema通过率、检索重叠度τ、验证器误报率ε)未给具体权重和阈值 → QA量无法预估
- **目标**: 给出可复现、可调的公式；上线即能看到QA告警曲线与阈值对应关系

#### 量化公式定义

| 元素 | 量化方式 |
|------|----------|
| Schema通过率 | schema_pass_rate = valid_fields / total_fields (0-1) |
| 检索重叠度τ | Jaccard(retrieved_chunk_ids, expected_key_event_ids)；默认阈值0.75 |
| 验证器误报率ε | false_positive / total_checks，7×24小时滚动窗口 |
| 综合置信度 | **confidence = 0.4×schema_pass_rate + 0.3×τ + 0.3×(1-ε)** |
| 人工QA触发 | **confidence < 0.85 OR first_episode == True** |

#### 配置示例(config.yaml)

```yaml
qa_control:
  weights:
    schema: 0.4      # Schema通过率权重
    overlap: 0.3     # 检索重叠度权重  
    fp_rate: 0.3     # 验证器误报率权重
  threshold: 0.85    # QA触发阈值
  overlap_min: 0.75  # 检索重叠度最低要求
  fp_rate_max: 0.05  # 验证器误报率上限
```

#### 代码实现示例

```python
def calculate_confidence(scene_result, qa_history):
    schema_rate = scene_result.valid_fields / scene_result.total_fields
    overlap_score = jaccard_similarity(
        scene_result.retrieved_chunks, 
        scene_result.expected_events
    )
    fp_rate = qa_history.get_fp_rate_7d()  # 7天滚动窗口
    
    confidence = (
        cfg.QA.weights.schema * schema_rate +
        cfg.QA.weights.overlap * overlap_score +
        cfg.QA.weights.fp_rate * (1 - fp_rate)
    )
    
    if confidence < cfg.QA.threshold or scene_result.episode_idx == 0:
        qa_manager.submit_for_review(scene_result, priority="high", confidence=confidence)
    
    return confidence
```

#### 验收指标
- 过去1周生产流量：置信度≥0.85的场景误检率≤3%；<0.85的场景真问题命中率≥80%
- QA人工处理量下降≥30%相比无量化前基线

### P0-3: 概要Schema强制输出main_characters/location

#### 背景与目标
- **问题**: 场景扩展阶段需要知道"谁在场""在哪里"做检索，但现有scene_summary中该信息非结构化，难以机器解析
- **目标**: 在Outline JSON增加两字段，并在Prompt中硬性要求LLM输出；同步更新Schema验证&检索逻辑

#### JSON Schema变更

```json
{
  "type": "object",
  "required": ["scene_id", "scene_summary", "main_characters", "location"],
  "properties": {
    "scene_id": { "type": "string" },
    "scene_summary": { "type": "string" },
    "main_characters": {
      "type": "array",
      "items": { "type": "string" },
      "minItems": 1
    },
    "location": { "type": "string" }
  }
}
```

#### Prompt修改示例

```
…
请输出JSON数组，每个元素包含：
- scene_id
- scene_summary  
- main_characters（列出该场景核心角色，字符串数组）
- location（该场景主要发生地点）

**重要：必须严格遵守上述键名，且不输出Markdown或多余文字。**
```

#### 检索&校验调整
- **检索增强**: 关键词派生 = {main_characters} ∪ {location} → 作为二次查询term
- **一致性校验**: 若后续场景JSON的speakers不全属于对应main_characters，标记一致性告警

#### 兼容性处理

```python
class SchemaVersionMigrator:
    def migrate_v1_0_to_v1_1(self, old_outline):
        """v1.0 → v1.1升级：自动插入空数组/UNKNOWN"""
        for scene in old_outline:
            if 'main_characters' not in scene:
                scene['main_characters'] = ["UNKNOWN"]
            if 'location' not in scene:
                scene['location'] = "UNKNOWN"
        return old_outline
```

#### 验收指标
- 新生成的Outline 100%通过新Schema校验
- 再跑一次端到端测试，场景检索丢失率下降≥10pp

### 改动汇总与收益

| 改动 | 主要收益 | 代码改动面 |
|------|----------|------------|
| 检索动态兜底 | 保证生成信息完整，压缩修复回合 | retrieval_rag.py, monitoring.py |
| 置信度量化 | QA流程可预测、成本可控 | qa_control.py, monitoring.py, 配置 |
| 概要角色/地点 | 提升后续检索精准度&一致性检测 | outline_generation.py, JSON Schema, Prompt |

**完成以上3项，即可把核心链路锁定在可监控、可迭代的MVP绿色通道，为后续高阶功能铺平道路。**

## 脚本纯净性与场景图生成补丁方案（新增需求）

基于最新业务需求，需要补充两项关键功能：确保脚本文件仅包含「对话+旁白」的纯净性，以及为每个场景自动生成场景图描述文件。

### 需求1: 脚本纯净性保障（确保script仅含dialogue+narration）

#### 背景与目标
- **问题**: 现有流程可能在script中混入舞台指令、镜头说明等非对话旁白内容
- **目标**: 最终script文件100%纯净，仅包含dialogue和narration，禁止任何stage directions等杂质

#### 实施方案

| 环节 | 现状 | 问题 | 必须改动 |
|------|------|------|----------|
| **Prompt约束** | 扩展Prompt已提醒"不要冗余" | 未显式禁止舞台指令(stage directions)等标签 | 在Stage C Prompt加硬性条款：禁止包含任何camera、scene_image、action_note、SFX等字段或括号指令；仅允许"dialogue"与"narration" |
| **SceneSchema** | 现有：dialogue[]、narration[]，但允许可选字段 | Schema本身没设additionalProperties=false | 修改JSON Schema："additionalProperties": false；其它字段（如images）放到独立文件 |
| **规则验证** | 只校验必填字段存在 | 未检出非法键/花括号注释 | 在规则引擎增加"非白名单键"检测：若出现stage_dir、action等立即fail&走修复 |
| **修复策略** | 重新生成或Patch | LLM可能再次写入指令 | 在修复提示里重复白名单字段，并示例非法字段会被拒绝 |

#### 技术实现

**更新后的SceneSchema:**
```json
{
  "type": "object",
  "required": ["scene_id", "dialogue", "narration"],
  "additionalProperties": false,
  "properties": {
    "scene_id": { "type": "string" },
    "dialogue": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["speaker", "text"],
        "additionalProperties": false,
        "properties": {
          "speaker": { "type": "string" },
          "text": { "type": "string" }
        }
      }
    },
    "narration": {
      "type": "array",
      "items": { "type": "string" }
    }
  }
}
```

**非法字段检测规则:**
```python
FORBIDDEN_FIELDS = ["stage_dir", "action", "camera", "scene_image", "SFX", "sound_effect", "music", "fade", "pan"]
FORBIDDEN_REGEX = r"[\[\(].*?(pan|fade|SFX|camera|action).*?[\]\)]"

def validate_script_purity(scene_json):
    # 检查非法字段
    for forbidden in FORBIDDEN_FIELDS:
        if forbidden in scene_json:
            return False, f"Forbidden field detected: {forbidden}"
    
    # 检查括号指令
    full_text = json.dumps(scene_json)
    if re.search(FORBIDDEN_REGEX, full_text, re.IGNORECASE):
        return False, "Stage direction patterns detected in text"
    
    return True, "Script purity validated"
```

#### 验收指标
- 随机抽检1,000行脚本，非法字段出现率 = 0%
- Regex: `[\[\(].*?(pan|fade|SFX|camera).*?[\]\)]` 命中数 = 0

### 需求2: 场景图描述自动生成

#### 设计思路

| 要素 | 设计 |
|------|------|
| **文件拆分** | `scene_XXX_script.json` 只含对话+旁白<br/>`scene_XXX_images.json` 只含图描述数组 |
| **字段结构** | `{"scene_id": "...", "images": [{"index":1, "description":"...", "approx_words":80}, ...]}` |
| **张数算法** | `N = ceil(total_words / 120)`（≈30秒语速150wpm → 120词/图 ≈ 声画匹配） |
| **生成逻辑** | Stage C扩展时，主模型生成script_json；随后调用辅助Prompt按word_count规则输出images_json |
| **验证规则** | `len(images) == N` AND 每张`approx_words ≤ 140` AND 描述禁止对话，纯环境/动作/情绪 |

#### 场景图生成Prompt示例

```
你是叙事场景美术规划助手。已知 scene_script_words = 350，
请按"每张图最多覆盖 120 个单词"规则，计算需要几张图并输出JSON：

{
  "scene_id": "Chap3_Sc2",
  "images":[
     {"index":1,"description":"昏暗的图书馆内，古老书架高耸，蜡烛微光摇曳，营造神秘氛围","approx_words":120},
     {"index":2,"description":"主角表情凝重地翻阅厚重典籍，眉头紧锁，背景书页飞舞","approx_words":115},
     {"index":3,"description":"突然发现关键线索，眼神一亮，手指指向书页某处，周围光线聚焦","approx_words":115}
  ]
}

**重要要求:**
- 描述仅包含环境、动作、情绪，禁止包含任何对话内容
- 每张图approx_words必须≤140
- 仅输出JSON，不要解释

**仅输出 JSON，不要解释。**
```

#### ImageSchema设计

```json
{
  "type": "object",
  "required": ["scene_id", "images"],
  "additionalProperties": false,
  "properties": {
    "scene_id": { "type": "string" },
    "images": {
      "type": "array",
      "minItems": 1,
      "items": {
        "type": "object",
        "required": ["index", "description", "approx_words"],
        "additionalProperties": false,
        "properties": {
          "index": { "type": "integer", "minimum": 1 },
          "description": { 
            "type": "string", 
            "maxLength": 200,
            "pattern": "^[^\"']*$"  // 禁止包含对话引号
          },
          "approx_words": { 
            "type": "integer", 
            "minimum": 50, 
            "maximum": 140 
          }
        }
      }
    }
  }
}
```

#### 流程增补

1. **Stage C生成脚本后** → 记录word_count
2. **调用Image-Prompt** → 产出images_json  
3. **通过ImageSchema验证器** → 校验张数/字段
4. **双文件QA** → 脚本按对白-旁白白名单，图描述按"无对白"白名单
5. **打包输出** → 

```
episode_1/
  ├─ scene_1_script.json    # 纯净对话+旁白
  ├─ scene_1_images.json    # 场景图描述
  ├─ scene_2_script.json
  ├─ scene_2_images.json
  └─ ...
```

#### 验证逻辑

```python
def validate_scene_images(images_json, script_word_count):
    expected_count = math.ceil(script_word_count / 120)
    actual_count = len(images_json["images"])
    
    if actual_count != expected_count:
        return False, f"Image count mismatch: expected {expected_count}, got {actual_count}"
    
    for img in images_json["images"]:
        # 检查字数限制
        if img["approx_words"] > 140:
            return False, f"Image {img['index']} exceeds word limit: {img['approx_words']}"
        
        # 检查是否包含对话（简单检测引号）
        if '"' in img["description"] or "'" in img["description"]:
            return False, f"Image {img['index']} contains dialogue quotes"
        
        # 检查对话关键词
        dialogue_keywords = ["说道", "回答", "问道", "叫道", "喊道"]
        if any(keyword in img["description"] for keyword in dialogue_keywords):
            return False, f"Image {img['index']} contains dialogue keywords"
    
    return True, "Scene images validated"
```

#### 兼容性处理

```python
class ImageSchemaVersionMigrator:
    def migrate_legacy_to_v1_1(self, scene_data):
        """处理旧流程无images.json的情况"""
        if "images" not in scene_data:
            word_count = self.estimate_word_count(scene_data)
            return {
                "scene_id": scene_data.get("scene_id", "UNKNOWN"),
                "images": [],  # 空数组标记需要人工补充
                "_migration_note": f"Legacy scene, estimated {word_count} words, requires manual image generation"
            }
        return scene_data
```

### 实施优先级调整

基于新增的两个关键需求，建议将实施优先级调整为：

**第一阶段增补（+2个P0需求）:**
- **P0-4: 脚本纯净性保障机制**: 更新SceneSchema、增加非法字段检测、修改Stage C Prompt硬性约束
- **P0-5: 场景图生成机制**: 实现ImageSchema、场景图生成Prompt、双文件验证和输出

**总工作量调整**: 原3人日 → 5人日（增加2人日处理新需求）

### 验收标准补充

#### 脚本纯净性验收
- [ ] 更新SceneSchema设置additionalProperties=false通过测试
- [ ] 非法字段检测规则覆盖所有FORBIDDEN_FIELDS  
- [ ] 1,000行脚本抽检非法字段出现率 = 0%
- [ ] Regex模式检测准确率 ≥ 95%

#### 场景图生成验收  
- [ ] ImageSchema验证通过率 = 100%
- [ ] 图片数量算法N=ceil(words/120)准确性验证
- [ ] 场景图描述禁止对话检测准确率 ≥ 95%
- [ ] 双文件输出结构符合预期格式

**满足以上要求，即可宣布"对白-旁白纯净 + 场景图可生成"两项需求100%就绪。**

## 工作流程（基于自适应检索增强与多层验证的生产版）

**整体流程图示概念:**
`自适应分块 & 安全扫描 -> 向量索引构建 -> 双重推理概要生成（确定性分集） -> [针对每个概要场景: 动态检索+影子模型验证 + LLM场景扩展 -> 规则验证 + 语义张力分析 -> 负反馈检索重新生成/轻量修复] -> 人工QA检查点（明确置信度） -> 最终输出`

**阶段详述:**

- **阶段 A: 自适应语义分块与安全扫描 (0次LLM调用)**
    - 输入: `save_witch_whole.json`文件中的单个章节数据。
    - 任务:
        1. **内容安全预扫描**: 检测并标记禁止内容（暴力、仇恨言论等），对风险内容进行隔离。
        2. **自适应语义分块**: 使用TextTiling或基于Transformer的语义相似度算法进行动态分段，避免固定粒度切断主题脉络。
        3. **安全过滤**: 移除HTML/脚本标签，过滤潜在的提示注入模式，防止RAG攻击。
        4. **分块边界验证**: 确保关键角色、事件、主题的语义完整性不被切断。
        5. **高熵全局表提取**: 从`key_characters`、`key_events`等提取角色名册、风格指南等全局信息。
    - 输出: 语义完整的安全分块向量索引 + 高熵全局表 + 风险内容标记报告。

- **阶段 B: 双重推理概要生成（确定性分集规则+结构化输出） (2次LLM调用/章节)**
    - 输入: 检索到的关键章节摘要 + 高熵全局表。
    - 任务: 
        - **双重调用**: 使用temperature 0.3和0.7分别生成章节概要。
        - **强制结构化输出**: 概要Schema强制包含main_characters(数组)和location(字符串)字段，严格遵守键名约定。
        - **差异对比**: 识别高方差的场景，标记为需要人工确认。
        - **确定性分集**: 应用明确规则（单集≤15分钟，必须在关键事件±3场景内结束）。
        - **重现性验证**: 确保相同手稿不同随机种子产生相同分集边界。
    - 输出: 经过双重验证的结构化章节概要列表(含main_characters/location) + 确定性分集边界 + 方差标记。

- **阶段 C: 动态检索场景扩展+影子模型验证+场景图生成 (N×3次LLM调用/章节, N为场景数)**
    - 输入: 
        - 单个场景概要(含main_characters/location) + 基于该概要检索的K=5-8个相关语义块
        - 缓存的全局表 + LLM调用计数器（上限50次/集）
    - 任务: 
        1. **动态上下文构建**: 基于main_characters和location进行精准检索，触发召回质量动态兜底机制(recall_estimate<0.85)。
        2. **LLM场景扩展**: 生成详细纯净脚本JSON，硬性禁止stage_dir、camera、SFX等非白名单字段，仅允许dialogue和narration。
        3. **场景图生成**: 基于脚本word_count，使用辅助Prompt按N=ceil(words/120)规则生成images_json。
        4. **影子模型验证**: 使用Gemini Flash对生成场景进行期望vs实际差异检测。
        5. **调用计数监控**: 超过50次调用时自动降级到简化流程。
    - 输出: 纯净脚本JSON + 独立场景图JSON + 影子模型验证报告 + 检索召回质量报告。

- **阶段 D: 脚本纯净性验证与语义张力分析 (0次LLM调用，1次轻量级分类模型调用)**
    - 输入: 纯净脚本JSON + 场景图JSON + 影子模型验证报告。
    - 任务: 
        1. **脚本纯净性验证**: 检查非白名单字段(FORBIDDEN_FIELDS)，Regex检测[\[\(].*?(pan|fade|SFX|camera).*?[\]\)]模式。
        2. **场景图验证**: 验证images数量N=ceil(total_words/120)，每张approx_words≤140，描述无对话内容。
        3. **JSON Schema验证**: 检查SceneSchema和ImageSchema格式符合性。
        4. **确定性节奏指标**: WPM、说话人转换频率、叙述密度。
        5. **语义张力分析**: 使用轻量级分类模型计算新引入障碍数量、冲突升级程度。
        6. **内容异常检测**: N-gram哈希重复检测、角色丢失检测。
    - 输出: 脚本纯净性验证报告 + 场景图验证报告 + 多维质量指标（节奏+张力） + 异常标记。

- **阶段 E: 负反馈检索重新生成与纯净性修复 (0-2次LLM调用/问题场景)**
    - 输入: 包含错误的脚本JSON + 场景图JSON + 验证报告 + 负反馈检索能力。
    - 任务: 
        - **纯净性修复优先**: 若检测到非法字段，优先重新生成脚本，在修复提示中重复白名单字段约束。
        - **场景图修复**: 若images数量或内容不符合要求，重新调用场景图生成Prompt。
        - **负反馈检索**: 验证器指出事实X错误时，检索包含X或与X矛盾的分块。
        - **检索范围扩展**: 标准检索失败时自动扩大检索范围（K=8→12）。
        - **智能重新生成**: 基于负反馈信息重新生成完整脚本JSON和场景图JSON。
        - **修复上限控制**: 单场景最多修复2次，超出后标记人工审核。
    - 输出: 基于负反馈改进的纯净脚本JSON + 修复后的场景图JSON。

- **阶段 F: 人工QA检查点（量化置信度公式） (0次LLM调用)**
    - 输入: 所有通过验证的场景JSON对象列表 + 各阶段质量指标。
    - 任务:
        1. **量化置信度计算**: confidence = 0.4×schema_pass_rate + 0.3×overlap_score + 0.3×(1-fp_rate)，各参数明确定义和权重。
        2. **QA触发条件**: 
            - confidence < 0.85 OR first_episode == True，可复现可调参
            - 检索重叠度 < 0.75 OR 验证器误报率 > 0.05：升级审核
            - 内容异常检测命中：升级审核
        3. **应急处理能力**: 
            - 向量数据库故障：回退到预生成章节摘要
            - LLM配额超限：队列处理并通知运维
        4. **内容监控**: 0.5%随机采样进行N-gram重复检测和质量异常检测。
    - 输出: 经过量化QA确认的最终音频剧本 + 置信度评分历史 + 运营监控报告。

**生产关键指标监控:**

| 指标类别 | 关键指标 | 目标阈值 | 监控频率 |
|----------|----------|----------|----------|
| **检索质量** | Recall@K (key_events→chunks) | ≥90% | 实时 |
| **P0-1:检索兜底** | 兜底触发率 (recall<0.85) | ≤20% | 实时 |
| **P0-1:兜底性能** | 兜底处理延迟 | ≤300ms P95 | 实时 |
| **P0-2:置信度** | 量化置信度分布 | 均值≥0.85 | 小时报 |
| **P0-2:QA效率** | QA人工处理量下降 | ≥30% vs基线 | 周报 |
| **P0-3:Schema** | 新概要Schema通过率 | 100% | 实时 |
| **P0-3:检索精度** | 场景检索丢失率下降 | ≥10pp vs基线 | 日报 |
| **P0-4:脚本纯净性** | 非法字段检测率 | 0% | 实时 |
| **P0-4:舞台指令** | Regex模式命中数 | 0 | 实时 |
| **P0-5:场景图数量** | 图片数量算法准确性 | 100% | 实时 |
| **P0-5:图描述质量** | 禁止对话检测准确率 | ≥95% | 日报 |
| **验证精度** | 验证器误报率 | ≤5% | 日报 |
| **分集一致性** | 相同手稿分集边界重现率 | 100% | 上线前验证 |
| **冷启动性能** | 手稿到首集草稿时长 | ≤SLA要求 | 实时 |
| **内容安全** | 禁止内容泄漏率 | 0% | 实时+人工抽查 |
| **成本控制** | LLM调用次数/集 | ≤50次 | 实时 |
| **响应延迟** | 单场景扩展时长 | ≤10秒 P95 | 实时 |

**总计API调用估算（考虑新增组件）**:
- **阶段A (自适应分块+安全扫描)**: 0次 LLM调用，1次分类模型调用（分块算法）。
- **阶段B (双重概要)**: 2次 LLM调用 / 章节。
- **阶段C (动态扩展+影子验证)**: N×2次 LLM调用 / 章节 (N≈10，包含主模型+影子模型)。
- **阶段D (语义张力分析)**: 0次 LLM调用，1次轻量级分类模型调用。
- **阶段E (负反馈重新生成)**: 平均0.3次 LLM调用 / 章节（仅20%场景需要，包含负反馈检索）。
- **阶段F (QA检查点)**: 0次 LLM调用。
- **小计**: **一个典型章节需要约22-23次LLM调用（包含影子模型），但单次Token消耗大幅降低，总成本仍显著优于原方案。**

## 基于2M上下文与实际数据的音频剧本生成架构设计

### A. JSON结构解析与全局上下文准备

#### 目标与价值
(同前，但强调输入`schema_version`的记录)

#### 技术实现
1.  **结构化数据深度解析**: (同前)
2.  **角色关系与称呼映射构建**: (同前)
3.  **全局上下文封装与版本化**:
    -   (同前) ...整合。
    -   在最终输出的全局上下文对象中，**嵌入一个`input_schema_version`字段**，记录所解析的 `save_witch_whole.json` 的Schema版本（例如，根据文件内元数据或配置文件确定）。这有助于后续的兼容性处理和问题追溯。

### B. LLM章节概要生成 (LLM Chapter Outline Generation)

#### 目标与价值
在不牺牲对整个章节完整理解的前提下，首先让LLM进行一次"粗加工"，提炼出章节的核心场景脉络。这既为后续的"精加工"提供了结构指引，也作为一种降低LLM在超长上下文中直接生成全部细节时可能出现的"失焦"或"遗忘"风险的手段。

#### 技术实现
1.  **输入**: 阶段A输出的包含`input_schema_version`的全局上下文对象。
2.  **Prompt工程**:
    -   指示LLM完整阅读并理解提供的全局上下文（包括`narrative.content`、`key_events`、`key_characters`等）。
    -   要求LLM基于其理解，识别出本章节中所有构成独立场景的关键节点/事件/转折点。
    -   输出格式要求为一个JSON对象列表，每个对象代表一个待生成的场景，至少包含：
        -   `scene_id`: 一个唯一的场景标识符（例如，`ChapX_ScY`，或基于`key_event`的ID）。
        -   `scene_summary`: 对该场景核心内容、主要动作或对话焦点的一句话概括。
        -   `main_characters`: 该场景的主要角色列表（字符串数组），用于后续检索增强和角色一致性校验。
        -   `location`: 该场景发生的主要地点（字符串），用于检索相关的场景描述和背景信息。
        -   （可选）`related_key_event_ids`: 与此场景直接相关的原始`key_event`的ID列表。
3.  **指令强调**: 在Prompt的最后，用简洁明确的语言重申最重要的输出格式要求，例如："**重要输出指示**：请确保你的回复是一个JSON数组，其中每个元素都是一个包含`scene_id` (字符串)、`scene_summary` (字符串)、`main_characters` (字符串数组)和`location` (字符串)键的JSON对象。不要包含任何markdown标记或解释性文本。"

### C. LLM逐场景扩展与JSON生成 (LLM Per-Scene Expansion & JSON Generation)

#### 目标与价值
针对概要中的每个场景"草稿"，利用LLM进行"精加工"。通过将LLM的注意力聚焦在单个场景的细节扩展上，同时让其能够随时查阅（通过缓存的）全局上下文以确保一致性和准确性，从而在控制推理复杂度的同时，生成高质量、符合整体设定的场景内容。

#### 技术实现
1.  **迭代处理**: 系统遍历阶段B生成的场景概要列表。
2.  **上下文缓存策略**: 对于当前章节的全局上下文对象（阶段A的输出），在首次用于本阶段或后续修复阶段时，通过Vertex AI API的`context_cache_id`参数（例如，使用章节内容的哈希值作为ID）将其提交并缓存。后续针对同一章节内其他场景的扩展调用，以及修复调用，都应使用相同的`context_cache_id`来复用已缓存的上下文，从而大幅减少计费Token和处理时间。
3.  **Prompt工程 (针对单个场景扩展)**:
    -   **输入**: 当前迭代到的`{scene_id, scene_summary, main_characters, location, ?related_key_event_ids}`对象，以及通过`context_cache_id`引用的、已缓存的全局上下文。
    -   **核心指令**: 指示LLM当前任务是为给定的`scene_id`和`scene_summary`扩展生成完整的场景JSON。
    -   **检索增强指导**: 基于`main_characters`和`location`字段进行精准的语义块检索，提升上下文相关性。
    -   **聚焦与参考**: 强调LLM应主要依据`scene_summary`来构建场景核心，但必须不断参考完整的（缓存的）全局上下文中的相关信息（如`narrative.content`的对应段落、`key_events`中与`related_key_event_ids`相关的详细描述、所有`key_characters`的特征和关系、全局称呼映射等）来丰富细节、确保准确性和一致性，并确保当前场景的生成服务于整体故事结构和发展趋势。
    -   **新增**: 指示LLM在必要时（如角色首次出场或为了清晰度）通过旁白明确介绍角色。例如："罗兰王子（ caractérisation brève ）走进房间，脸上带着疑虑。"
    -   **新增**: 强调每个对话行必须明确说话者，确保听众能够清晰识别。
    -   **新增**: 指导LLM注意控制场景的节奏和情感波动，确保场景既能有效推动情节，又具有吸引力。提示LLM适当运用对话和旁白来营造紧张或轻松的氛围，并注意场景内容不要过于冗长，以便控制整体时长。
    -   **输出要求**: 严格要求输出单个、符合预定义`SceneSchema`的JSON对象。
4.  **指令强调**: 同阶段B，在Prompt末尾重申关键的输出格式要求。例如："**重要输出指示**：请确保你的回复是一个单个JSON对象，严格符合SceneSchema的规范。其中，旁白应适时介绍出场角色，每个`dialogue`条目必须有明确的`speaker`。场景内容应注意节奏和情感表达，避免不必要的冗长。不要包含任何其他文字或解释。"

### D. 规则引擎验证与节奏控制

#### 目标与价值
(同前)

#### 技术实现
(基本同前，但现在明确是针对阶段C输出的单个场景JSON进行。部分全局一致性规则（如`key_event`是否都覆盖了）会移到最终校验阶段F。)
1.  **JSON Schema与格式校验**: (同前)
2.  **内容一致性校验 (参照全局上下文)**:
    -   **角色校验**: (同前，利用全局角色库和称呼映射表)
    -   **事件关联校验**: (同前，检查与`related_key_event_ids`的对应)
    -   **首次出场逻辑**: (同前)
3.  **音频节奏与时长校验模块**: (同前) 新增：检查场景的预估时长（例如基于总字数和标准语速）是否在合理范围，避免过长或过短的场景，确保节奏紧凑。
4.  **初步质量检查模块**: 新增：检查是否有明显的内容缺失、逻辑不通、情节发展不合理、角色行为不符合设定或对话生硬等问题。
5.  **新增：动态节奏评估与调整尝试模块**: 基于规则引擎对场景内的对话、旁白疏密度进行评估，识别潜在的节奏过慢或冗余部分。可尝试进行初步的自动调整（如修剪冗余描述、提示特定对话段落可能需要加速等），或更明确地标记给后续LLM修复阶段。
6.  **生成验证报告**: (同前，针对单个场景)

### E. LLM自我修复循环 (JSON Patch机制)

#### 目标与价值
(同前)

#### 技术实现
1.  **错误定位与分类**: (同前)
2.  **差异化修复提示构建 (要求JSON Patch)**:
    -   **输入**: 错误的场景JSON，错误描述，以及通过`context_cache_id`引用的、已缓存的全局上下文。
    -   **提示结构**: "重要：请参考通过context_cache_id提供的完整章节上下文。对于以下场景JSON：`{FAULTY_SCENE_JSON}`，规则检查发现这些问题：`{ERROR_DESCRIPTIONS}`。请生成一个JSON Patch (RFC 6902)数组来修正这些错误，确保修正后的内容与全局上下文保持一致。例如：`[{\"op\": \"replace\", \"path\": \"/dialogue/2/speaker\", \"value\": \"角色名\"}]`。"
3.  **指令强调**: Prompt末尾强调："**重要输出指示**：你的回复必须是一个JSON Patch数组 (RFC 6902)。不要包含任何markdown标记或解释性文本。"
4.  **本地应用Patch**: (同前)
5.  **LLM调用与重试**: (同前)

### F. 最终校验、高级评估与输出

#### 目标与价值
(同前)

#### 技术实现
1.  **章节级全局一致性检查**: (同前，现在有了阶段B的概要，可以比对场景列表是否完整覆盖了概要意图，以及所有重要`key_events`是否都有体现)。新增：检查各场景之间是否存在逻辑断裂或情节跳跃，确保前后场景自然衔接，符合整体故事发展趋势。
2.  **节奏曲线自动评估 (增强)**:
    -   **评分机制**: 针对每个通过的场景JSON，使用一个轻量级LLM（如Gemini Flash，同样可以利用缓存的全局上下文中的相关部分，或仅传入场景文本）或专门训练的分类器，从多个维度（如：情绪激烈度[1-5]，情节紧张度[1-5]，信息密度[1-5]）对场景的核心内容（特别是对话和关键旁白）进行打分。
    -   **曲线生成与可视化**: 将每个场景的各项评分按顺序绘制成多条曲线图，形成章节的"多维情感/节奏剖面图"。
    -   **模式识别与标记**: 自动识别并标记出可能的"平淡期"（连续低分）、"冗余高峰"（不必要的连续高分）或与预期`narrative.themes`不符的情感走向，这些标记将作为后续调整（无论是自动微调尝试还是人工介入）的重要依据。
3.  **整体质量与节奏评估**: (同前)
4.  **剧本标准化与打包**: (同前)
5.  **生成交付物**: (同前，质量报告中可包含节奏曲线图和分析)。

## 技术组件设计

### 核心组件
- **自适应语义分块器 (Adaptive Semantic Chunker)**: 使用TextTiling或基于Transformer的算法进行动态语义分段，避免固定粒度切断主题脉络。集成分块边界验证机制。
- **内容安全扫描器 (Content Safety Scanner)**: 在索引前进行多层安全检查：禁止内容检测、HTML/脚本过滤、提示注入模式识别，防止RAG攻击。
- **检索保真动态兜底引擎 (Retrieval Fidelity Fallback Engine)**: **新增核心组件**。支持标准K=5-8检索、负反馈检索和召回质量动态兜底(recall_estimate<0.85时触发混合检索)，检索范围可动态扩展（K=8→12）。
- **结构化概要协调器 (Structured Outline Coordinator)**: 实现temperature差异对比、强制main_characters/location字段输出、确定性分集规则（≤15分钟，±3场景内结束）、重现性验证。
- **脚本纯净性保障引擎 (Script Purity Guardian Engine)**: **新增关键组件**。实现硬性禁止非白名单字段(stage_dir、camera、SFX等)，Regex检测舞台指令模式，确保脚本100%纯净。
- **场景图生成协调器 (Scene Image Generation Coordinator)**: **新增关键组件**。基于word_count按N=ceil(words/120)规则生成场景图描述，验证无对话内容，支持双文件输出结构。
- **影子模型验证器 (Shadow Model Validator)**: 使用轻量级模型（Gemini Flash）对主模型生成的场景进行期望vs实际差异检测，提前发现幻觉。**配置为可选组件**，支持灰度开启（高复杂场景开启，默认关闭），避免15%成本增加。
- **语义张力分析器 (Semantic Tension Analyzer)**: 轻量级分类模型计算新引入障碍数量、冲突升级程度，补充确定性节奏指标的戏剧张力盲点。
- **智能修复管理器 (Smart Repair Manager)**: 集成负反馈检索、纯净性修复优先、场景图修复、检索范围扩展、修复上限控制（2次/场景），避免修复循环。
- **量化置信度QA控制器 (Quantified Confidence QA Controller)**: **核心升级组件**。实现confidence = 0.4×schema_pass_rate + 0.3×overlap_score + 0.3×(1-fp_rate)的量化置信度计算，支持双文件审核机制。
- **LLM调用上限控制器 (LLM Call Limit Controller)**: 实现50次调用上限监控和明确的降级流程处理，考虑场景图生成的额外调用需求。

### 辅助组件
- **自适应分块算法引擎 (Adaptive Chunking Algorithm Engine)**: 集成TextTiling、基于Transformer的语义相似度计算，动态选择最佳分块策略。
- **提示注入检测器 (Prompt Injection Detector)**: 识别和过滤潜在的RAG攻击模式，使用正则表达式和机器学习模型组合检测。
- **内容异常监控器 (Content Anomaly Monitor)**: 实施N-gram哈希重复检测、角色丢失检测，0.5%随机采样异常检测管道。
- **确定性指标计算引擎 (Deterministic Metrics Engine)**: 计算WPM、说话人转换频率、叙述密度，提供快速节奏质量评估。
- **LLM调用计数器 (LLM Call Counter)**: 实时追踪每集LLM调用次数，超过50次时触发明确的降级流程（详见降级策略）。
- **检索重叠度计算器 (Retrieval Overlap Calculator)**: 计算检索内容与预期的重叠度，作为置信度评估的关键指标。
- **应急回退管理器 (Emergency Fallback Manager)**: 处理向量数据库故障（回退到预生成摘要）、LLM配额超限（队列处理）等应急情况。
- **分集重现性验证器 (Episode Reproducibility Validator)**: 确保相同手稿在不同随机种子下产生相同的分集边界，维护分集一致性。
- **风格一致性控制器 (Style Consistency Controller)**: 维护显式tone/genre_style字段，防止长期生产中的风格漂移。
- **Schema版本迁移管理器 (Schema Version Migration Manager)**: 处理向量索引的版本迁移，支持双索引维护和平滑切换。

### 生产监控组件
- **多维质量监控仪表板 (Multi-Dimensional Quality Dashboard)**: 实时显示检索质量、验证精度、分集一致性、响应延迟等关键指标。
- **成本优化追踪器 (Cost Optimization Tracker)**: 监控Token消耗、API调用频率、检索效率，确保成本在预算范围内。
- **内容安全监控系统 (Content Safety Monitoring System)**: 实时检测禁止内容泄漏，结合人工抽查确保0%泄漏率。
- **性能瓶颈分析器 (Performance Bottleneck Analyzer)**: 识别冷启动延迟、检索慢查询、LLM调用超时等性能问题。

### 关键数据指标与阈值定义
- **检索质量**: Recall@K (key_events→chunks) ≥ 90%
- **验证精度**: 验证器误报率 ≤ 5%（保持人工QA可操作性）
- **分集重现性**: 相同手稿分集边界重现率 = 100%
- **冷启动性能**: 手稿到首集草稿时长 ≤ SLA要求
- **内容安全**: 禁止内容泄漏率 = 0%
- **成本控制**: LLM调用次数 ≤ 50次/集
- **响应延迟**: 单场景扩展时长 ≤ 10秒 P95
- **综合置信度**: Schema通过率 AND 检索重叠度≥τ AND 验证器误报率≤ε

## LLM调用上限降级流程详述

### 50次调用上限触发的明确降级策略

#### 降级触发条件
- **硬阈值**: 单集LLM调用次数达到50次
- **软预警**: 调用次数达到40次时发出预警，开始准备降级
- **监控指标**: 实时追踪主模型调用、影子模型调用、修复调用的分类计数

#### 降级执行流程

```
if (llm_call_count >= 50):
    trigger_degraded_mode()
    log_alert("LLM_CALL_LIMIT_EXCEEDED", episode_id, current_count)
    return execute_simplified_pipeline()

if (llm_call_count >= 40):
    log_warning("LLM_CALL_APPROACHING_LIMIT", episode_id, current_count)
    disable_shadow_model_validation()  # 立即禁用影子模型验证
    set_max_repair_attempts(1)         # 降低修复尝试次数
```

#### 简化流程 (Simplified Pipeline)
1. **跳过影子模型验证**: 停止所有影子模型调用，直接使用主模型输出
2. **降级修复策略**: 
   - 跳过负反馈检索重新生成
   - 仅使用确定性规则验证，错误场景直接标记人工审核
   - 禁用JSON Patch修复
3. **保持基本质量**: 继续执行Schema验证和WPM等确定性指标检查
4. **输出标记**: 在最终输出中明确标记"DEGRADED_MODE"，通知下游系统

#### 运维响应手册 (Operations Runbook)

| 告警级别 | 触发条件 | 立即响应 | 后续行动 |
|----------|----------|----------|----------|
| **WARNING** | 调用数≥40次 | 1. 检查是否为复杂异常场景<br/>2. 考虑人工介入 | 优化检索参数，减少无效调用 |
| **CRITICAL** | 调用数≥50次 | 1. 确认降级模式已启动<br/>2. 标记该集需要质量复查 | 1. 分析调用分布<br/>2. 调整温度参数<br/>3. 优化Prompt设计 |
| **EMERGENCY** | 多集连续降级 | 1. 暂停自动生成<br/>2. 升级到技术负责人 | 1. 系统性排查<br/>2. 可能的模型切换<br/>3. 紧急补丁部署 |

#### 成本与质量平衡机制
- **成本保护**: 确保单集成本不会失控
- **质量保底**: 即使在降级模式下，仍保持基本的结构和格式正确性
- **可追溯性**: 完整记录降级原因和影响范围，便于后续分析优化

## 成本控制与风险管理

### 成本控制策略
- **检索增强上下文 (RAG)**: **核心策略**。通过语义分块和向量检索，每次LLM调用仅使用2-3k tokens的相关上下文，而非数百万token的完整章节。相比原方案实现10-20倍的Token成本降低。
- **高熵全局表缓存**: 仅对角色名册、风格指南等高价值信息（数千token）使用缓存，避免巨大静态缓存的TTL和版本升级风险。
- **确定性节奏分析**: 使用WPM、说话人转换频率、叙述密度等确定性指标替代LLM分析，消除70%以上的节奏评估LLM调用成本。
- **双重推理概要生成**: 虽然增加1次概要调用，但通过前置稳定性检查，显著降低下游场景扩展的修复需求，整体减少修复循环成本。
- **重新生成优先修复策略**: 避免JSON Patch震荡循环，单次修复完成，减少多轮修复的累积成本。
- **边缘情况LLM分析**: 仅在确定性指标无法判断时才使用轻量级LLM，大幅减少非必要的LLM调用。
- **并行处理能力**: 多个场景可并行扩展，多个"集"可并行生成，提高系统吞吐量和时间效率。
- **动态模型选择**: 主流程使用高性能模型，辅助任务（如边缘情况分析）使用成本更低的轻量级模型。

### 风险管理机制
- **上下文召回质量限制的缓解**: 
    - 通过检索增强避免依赖超过600k tokens的长上下文召回
    - 动态上下文确保每次调用都在模型的最佳性能范围内
- **缓存稳定性风险消除**: 
    - 仅缓存小量高熵全局表，避免巨大缓存的TTL限制和模型升级风险
    - 动态检索消除对单一巨大缓存对象的依赖
- **修复循环收敛性保证**: 
    - 优先使用重新生成策略，避免JSON Patch的震荡问题
    - 单次修复+diff记录机制，确保修复过程可预测和可控
- **规则引擎复杂度控制**: 
    - 确定性指标简化了节奏分析的规则复杂度
    - 人工QA检查点减轻了自动化规则的负担
- **双重推理概要稳定性**: 
    - 通过temperature对比识别不稳定的场景，前置人工确认
    - 降低概要质量问题对下游扩展的影响
- **人工QA检查点风险缓解**: 
    - 强制首集审核捕获系统性问题
    - 置信度阈值自动升级机制
    - 二级审核体系平衡效率和质量
- **优雅降级策略完善**: 
    - 在长上下文模型不可用时自动切换到备用模式
    - 成本激增时动态调整检索参数和模型选择
    - Schema版本不兼容时的兼容性适配和默认值填充

### 监控与告警增强
- **成本监控**: 实时追踪Token消耗、API调用频率、检索效率等关键成本指标
- **质量监控**: 监控确定性指标分布、人工QA触发率、修复成功率等质量指标  
- **性能监控**: 追踪检索延迟、场景扩展时间、整体流程吞吐量等性能指标
- **稳定性监控**: 监控双重推理方差、缓存命中率、降级触发频率等稳定性指标

### 预算估算重新评估 (基于检索增强架构)
- **Token成本大幅降低**: 从原方案的数百万token/章节降低到<120k tokens/章节
- **调用效率提升**: 虽然调用次数略增（双重概要），但单次调用成本显著降低
- **总体成本优化**: 相比原方案预期降低80-90%的LLM API成本
- **人工成本平衡**: 增加的人工QA成本被大幅降低的LLM成本抵消，总体经济效益显著提升
- **成本可预测性**: 基于确定性指标和检索机制，成本更加可预测和可控

## 实施优先级与路线图

### 第一阶段：MVP关键短板补齐与核心引擎构建（3-4周）
**关键MVP短板优先级**: P0-1(检索动态兜底) → P0-2(置信度量化) → P0-3(概要结构化) → 其他核心组件

#### MVP关键短板（P0优先级，≈5人日）:
1. **P0-1: 检索保真动态兜底机制**: 实现recall_estimate监控和混合检索兜底，保证检索质量≥0.85，触发兜底<300ms。
2. **P0-2: 置信度公式量化**: 实现confidence = 0.4×schema + 0.3×overlap + 0.3×(1-fp_rate)的量化公式，使QA流程可预测可控。
3. **P0-3: 概要Schema强制输出main_characters/location**: 更新Outline Schema和Prompt，提升后续检索精准度≥10pp。
4. **P0-4: 脚本纯净性保障机制**: 更新SceneSchema设置additionalProperties=false，增加非法字段检测，修改Stage C Prompt硬性约束。
5. **P0-5: 场景图生成机制**: 实现ImageSchema、场景图生成Prompt、双文件验证和输出，支持N=ceil(words/120)算法。

#### 核心引擎组件（基础建设）:
6. **语义分块与向量索引构建器**: 实现章节内容的语义分块（1-2k tokens），建立向量数据库索引，支持相似度检索。
7. **结构化概要协调器**: 基于P0-3升级，实现temperature 0.3/0.7的双重概要生成和结构化输出验证。
8. **动态场景扩展协调器**: 基于P0-1和P0-4/P0-5升级，集成检索动态兜底机制、脚本纯净性保障和场景图生成。
9. **确定性节奏分析器**: 实现WPM、说话人转换频率、叙述密度的确定性计算，替代LLM节奏分析。
10. **智能修复管理器**: 基于P0-4升级，实现纯净性修复优先、场景图修复的综合修复策略。
11. **LLM调用上限控制器**: 实现50次调用上限监控和明确降级流程，考虑场景图生成的额外调用需求。
12. **端到端流程打通**: 跑通单章节的完整流程（分块→结构化概要→动态兜底检索→脚本+场景图扩展→纯净性验证→双文件输出）。

### 第二阶段：人工QA集成与监控优化（2-3周）
1. **量化QA工作流管理器**: 基于P0-2量化置信度公式，实现可预测的QA触发、任务分发、追踪和升级逻辑。
2. **检索质量监控系统**: 基于P0-1动态兜底机制，建立recall_estimate实时监控、兜底频率统计和K值自适应调整。
3. **多维监控与告警系统**: 建立成本、质量、性能、稳定性四维监控体系，重点监控置信度分布和检索保真度。
4. **边缘情况LLM分析器**: 实现仅在确定性指标无法判断时的轻量级LLM二次分析。
5. **风格一致性控制器**: 实现显式tone/genre_style字段的维护和注入，防止风格漂移。
6. **影子模型验证器（可选）**: 实现灰度配置机制，默认关闭，支持环境变量控制。

### 第三阶段：优雅降级与生产优化（2-3周）
1. **优雅降级控制器**: 实现在长上下文模型不可用或成本激增时的自动降级策略。
2. **Schema版本与兼容性管理器**: 完善输入JSON版本兼容性处理和默认值填充逻辑。
3. **并行处理优化**: 实现多场景并行扩展、多集并行生成，提升系统吞吐量。
4. **成本与性能深度优化**: 根据监控数据优化检索参数、模型选择、缓存策略。
5. **全面测试与部署准备**: 压力测试、边界条件测试、降级场景测试。

### 可选扩展功能（后续迭代）
- **混合检索增强**: 结合向量相似度和关键词匹配的混合检索策略
- **自适应检索参数**: 根据场景复杂度动态调整检索的K值和相关性阈值
- **多模型集成**: 支持不同LLM模型的动态选择和负载均衡
- **高级人工QA工具**: 提供可视化的场景对比、diff查看、批量审核等工具
- **跨章节一致性检查**: 基于向量检索的角色行为一致性分析

### 架构优化对比表（更新版）
| 维度 | 原"2M上下文缓存"版本 | 本"检索增强+确定性分析"版本 | 改进效果 |
|------|---------------------|----------------------------|----------|
| **核心上下文策略** | 单一巨大缓存对象（2M tokens） | 动态检索语义块（2-3k tokens/次） | 消除长上下文召回质量问题，提升稳定性 |
| **节奏分析方式** | LLM基础的曲线评估 | 确定性指标计算（WPM等） | 降低70%以上节奏分析成本，提升可靠性 |
| **修复策略** | JSON Patch循环修复 | 重新生成优先+轻量Patch备用 | 避免震荡循环，确保修复收敛性 |
| **概要生成** | 单次概要生成 | 双重推理概要（差异对比） | 前置稳定性检查，降低下游修复需求 |
| **质量保证** | 全自动化处理 | 人工QA检查点+二级审核 | 平衡自动化效率和内容质量保证 |
| **Token成本/章节** | 可能数百万tokens | <120k tokens | 降低80-90%的LLM API成本 |
| **调用延迟** | 可能30-60秒/次（巨大Prompt） | 一般<10秒/次（轻量Prompt） | 大幅提升响应速度和用户体验 |
| **缓存风险** | TTL限制、版本升级风险 | 仅小量全局表缓存 | 消除主要缓存稳定性风险 |
| **并行处理** | 受巨大缓存限制 | 天然支持并行扩展 | 提升系统吞吐量和可扩展性 |
| **降级能力** | 单点故障风险 | 多层降级策略 | 增强系统韧性和可用性 |

## 结论

通过采纳**检索增强上下文（RAG）**、**确定性节奏分析**、**双重推理概要保护**、**重新生成优先修复**、**人工QA检查点**以及**优雅降级策略**等一系列基于生产实践的架构改进，本优化架构显著提升了音频剧本生成系统的**稳定性、经济性、可维护性和质量保证能力**。

### 核心优势 (相比原2M上下文缓存方案)
1. **生产环境适用性大幅提升**: 通过检索增强避免超过600k tokens的长上下文召回质量问题，确保每次LLM调用都在最佳性能范围内。
2. **成本效益显著优化**: Token消耗从数百万降低到<120k/章节，实现80-90%的LLM API成本降低，总体经济效益显著提升。
3. **系统稳定性根本改善**: 消除巨大缓存的TTL限制和版本升级风险，通过重新生成策略避免JSON Patch震荡循环。
4. **节奏分析效率大幅提升**: 确定性指标（WPM、说话人转换频率、叙述密度）替代LLM分析，消除70%以上的节奏评估成本同时提升可靠性。
5. **质量保证体系完善**: 双重推理前置稳定性检查，人工QA检查点平衡自动化效率和内容质量，二级审核体系确保关键质量控制。
6. **系统韧性和可扩展性增强**: 多层优雅降级策略、天然并行处理能力、动态模型选择机制大幅提升系统可用性和吞吐量。

### 架构创新的生产价值
-   **从理论到实践的转变**: 从追求超长上下文的理论极限，转向适合生产环境约束的实用架构设计。
-   **成本与质量的平衡**: 通过智能的技术选择（确定性分析 vs LLM分析、检索 vs 缓存），在大幅降低成本的同时保持甚至提升内容质量。
-   **人机协作的优化**: 合理设置人工QA检查点，既保证内容质量又避免过度依赖人工审核的效率瓶颈。
-   **系统工程的成熟**: 从单纯的AI能力展示，升级为具备监控、降级、并行处理等生产级特性的完整系统。
-   **可运维性优先**: 通过明确的降级流程、灰度配置和Schema扩展，确保系统在生产环境中可控、可观测、可修复。

### 技术选择的合理性验证
本次架构调整基于以下经过验证的生产实践原则：
-   **检索增强 > 巨大缓存**: Google自身的RAG最佳实践证明了检索增强在实际部署中的优越性
-   **确定性指标 > LLM分析**: 在节奏分析等可计算任务上，确定性指标提供更可靠和经济的解决方案
-   **重新生成 > 复杂修复**: LLM更擅长生成而非编辑，重新生成策略避免了修复循环的复杂性
-   **人工检查点 > 全自动化**: 在创意内容生产中，关键节点的人工质量把关是行业标准做法

### 未来发展方向
此架构为音频剧本生成领域建立了一个既能充分利用大模型能力，又能在实际生产环境中稳定、经济、高质量运行的技术范式。它代表了从"让AI做所有事"向"让AI和人类协作做对的事"的成熟转变，为规模化、可持续的AI内容创作提供了坚实的工程基础。

**最终评估**: 经过基于生产实践建议的全面优化，本架构已从实验性的技术展示升级为具备完整生产就绪性的企业级AI剧本生产线，将有力推动高质量音频内容的自动化、规模化创作。

### 关键生产就绪性问题解答

基于review提出的五个核心问题，以下是架构的具体解答和验证计划：

#### 1. 检索保真度 (Retrieval Fidelity)
**问题**: key_events到chunks的Recall@K在现实噪声下的表现？
**解答**: 
- **测试计划**: 使用真实OCR错误、混合语言的测试数据集，测量不同K值（5,8,12）下的召回率
- **目标阈值**: Recall@8 ≥ 90%，Recall@12 ≥ 95%
- **应对措施**: 如达不到阈值，将扩展到混合检索（向量+关键词匹配）

#### 2. 验证器精度/召回率 (Validator Precision/Recall)
**问题**: 规则引擎的误报率和漏报率？
**解答**:
- **测试计划**: 使用标注的"真实不一致"和"真实一致"场景数据集进行验证
- **目标阈值**: 误报率 ≤ 5%，漏报率 ≤ 10%
- **应对措施**: 超出阈值时调整规则权重或引入机器学习增强验证

#### 3. 分集划分重现性 (Episode Split Reproducibility)
**问题**: 相同手稿不同随机种子是否产生相同分集边界？
**解答**:
- **确定性规则**: 单集≤15分钟，必须在关键事件±3场景内结束
- **锚定机制**: 基于key_events的UUID而非随机生成进行边界确定
- **测试验证**: 100次不同种子运行，分集边界重现率必须=100%

#### 4. 冷启动延迟 (Cold-Start Latency)
**问题**: 从手稿到首集草稿的时间是否满足SLA？
**解答**:
- **性能目标**: 冷启动≤5分钟（包含分块、索引、概要、首个场景扩展）
- **优化措施**: 并行分块+索引、预热向量数据库、缓存模板Prompt
- **监控机制**: 实时追踪P95延迟，超时熔断机制

#### 5. 内容安全政策 (Content Safety Policy)
**问题**: 原始文本包含禁止内容时的过滤机制？
**解答**:
- **多层过滤**: 分块前预扫描→索引时安全过滤→检索时二次过滤
- **红线内容**: 图像暴力、仇恨言论、性暴力内容
- **处理策略**: 风险内容隔离、人工审核标记、拒绝生成

### 具体优化建议实施计划

#### P0优先级（必须在第一阶段完成）
1. **自适应分块器**: 集成TextTiling算法，实现分块边界验证机制
2. **负反馈检索**: 实现"与X矛盾的分块检索"功能，避免修复循环

#### P1优先级（第二阶段完成）  
3. **语义张力指标**: 训练轻量级障碍数量分类模型，补充WPM等指标
4. **确定性分集规则**: 实现15分钟上限、±3场景内结束的硬性约束
5. **明确置信度公式**: Schema通过率 AND 检索重叠度≥0.7 AND 验证器误报率≤0.05

#### P2优先级（第三阶段完成）
6. **内容异常监控**: 实施0.5%随机采样的N-gram重复检测管道
7. **应急预案手册**: 
   - 向量数据库故障→回退预生成摘要
   - LLM配额超限→队列处理+运维通知
   - 内容安全告警→自动隔离+人工升级

### 成功验收标准（每阶段）

#### 第一阶段验收标准
- [ ] 自适应分块算法Recall@8 ≥ 90%
- [ ] 负反馈检索功能通过单元测试
- [ ] 影子模型验证准确率 ≥ 85%（灰度可配置）
- [ ] **P0-1**: 章节概要Schema包含main_characters和location字段，检索命中率提升验证
- [ ] **P0-2**: 50次调用上限触发降级流程，运维手册完成
- [ ] **P0-3**: 影子模型验证开关可通过环境变量控制
- [ ] 端到端流程完整跑通

#### 第二阶段验收标准  
- [ ] 语义张力分类模型准确率 ≥ 80%
- [ ] 分集边界重现率 = 100%（100次测试）
- [ ] 明确置信度公式验证误报率 ≤ 5%
- [ ] QA工作流人工测试通过

#### 第三阶段验收标准
- [ ] 内容异常检测召回率 ≥ 95%
- [ ] 应急预案演练通过
- [ ] 冷启动延迟P95 ≤ 5分钟
- [ ] 生产监控仪表板功能完整

### 风险缓解与应急计划

| 风险场景 | 检测方式 | 应急响应 | 预防措施 |
|----------|----------|----------|----------|
| 检索质量下降 | 实时Recall@K监控 | 扩大检索范围K值 | 定期重新训练向量模型 |
| 验证器误报激增 | 日报误报率统计 | 临时放宽阈值+人工兜底 | A/B测试规则调整 |
| 向量数据库故障 | 健康检查+延迟监控 | 切换到预生成摘要模式 | 双数据中心部署 |
| LLM服务中断 | API响应监控 | 降级到规则生成+队列处理 | 多厂商LLM备用 |
| 内容安全泄漏 | 0.5%随机采样检测 | 立即下线+人工审核 | 多层过滤+预训练模型 |


