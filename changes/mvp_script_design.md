# MVP音频剧本生成架构设计

## 执行摘要

本文档定义了一个最小可行产品(MVP)架构，专注于将长篇小说高效转换为高质量音频剧本的核心流程。该架构采用分层处理策略，通过语义分块、检索增强生成和结构化验证，实现从小说文本到音频剧本的自动化转换。

**核心目标**: 生成包含纯净对话和旁白的音频剧本，以及对应的场景图描述，确保内容质量、角色一致性和情节连贯性。

**设计原则**: 
- 专注核心功能，避免过度工程化
- 确保输出质量和一致性
- 控制成本和复杂度
- 支持人工质量检查点

## 系统概览

### 输入与输出

**输入**: 结构化小说数据文件 (`save_witch_whole.json`)，包含：
- 章节内容 (`narrative.content`)
- 关键事件 (`key_events`)
- 主要角色 (`key_characters`)
- 叙事主题 (`narrative.themes`)

**输出**: 
- 纯净音频剧本文件 (仅包含对话和旁白)
- 场景图描述文件 (环境、动作、情绪描述)

### 核心处理流程

系统采用四阶段处理流程：

1. **内容预处理与分块** - 将小说内容分解为语义完整的片段
2. **章节概要生成** - 提取场景结构和关键信息
3. **场景扩展与生成** - 生成详细的剧本内容和场景图
4. **质量验证与输出** - 确保内容纯净性和格式正确性

## 详细架构设计

### 阶段一：内容预处理与分块

#### 目标
将长篇小说内容分解为适合LLM处理的语义完整片段，建立检索索引以支持后续的上下文增强生成。

#### 核心组件

**语义分块器 (Semantic Chunker)**
- 使用自适应算法进行语义分段，避免切断完整的情节或对话
- 每个分块控制在1-2k tokens，确保语义完整性
- 保留分块间的关联关系和上下文信息

**向量索引构建器 (Vector Index Builder)**
- 为每个语义分块生成向量表示
- 建立基于相似度的检索索引
- 支持基于角色、地点、事件的多维检索

**全局信息提取器 (Global Info Extractor)**
- 从`key_characters`提取角色信息和关系映射
- 从`key_events`提取关键情节节点
- 构建全局角色称呼和行为模式库

#### 数据流
```
小说JSON → 语义分块 → 向量化 → 检索索引
         ↓
    全局信息提取 → 角色库 + 事件库
```

#### 输出
- 语义分块向量索引
- 全局角色信息库
- 关键事件映射表

### 阶段二：章节概要生成

#### 目标
基于完整章节理解，生成结构化的场景概要，为后续详细扩展提供框架指导。

#### 核心组件

**概要生成协调器 (Outline Generator)**
- 分析完整章节内容，识别独立场景节点
- 生成包含场景ID、摘要、主要角色和地点的结构化概要
- 确保场景划分的逻辑性和完整性

**结构化输出验证器 (Structured Output Validator)**
- 验证概要格式符合预定义Schema
- 确保必要字段完整性：scene_id、scene_summary、main_characters、location
- 检查场景逻辑连贯性

#### 处理逻辑
1. LLM接收完整章节上下文和全局信息
2. 识别章节中的关键场景转折点
3. 为每个场景生成结构化描述
4. 验证输出格式和内容完整性

#### 输出格式
```json
{
  "scenes": [
    {
      "scene_id": "Chap1_Sc1",
      "scene_summary": "场景核心内容概述",
      "main_characters": ["角色A", "角色B"],
      "location": "具体地点"
    }
  ]
}
```

### 阶段三：场景扩展与生成

#### 目标
基于场景概要和检索增强的上下文，生成详细的音频剧本内容和对应的场景图描述。

#### 核心组件

**动态检索引擎 (Dynamic Retrieval Engine)**
- 基于场景的main_characters和location进行精准检索
- 从向量索引中获取相关的语义分块
- 提供丰富的上下文信息支持场景扩展

**场景扩展生成器 (Scene Expansion Generator)**
- 接收场景概要和检索到的上下文信息
- 生成包含对话和旁白的详细剧本内容
- 确保角色一致性和情节连贯性

**场景图生成器 (Scene Image Generator)**
- 基于剧本内容的字数计算所需图片数量 (N = ceil(words/120))
- 生成环境、动作、情绪描述，不包含对话内容
- 每张图片描述控制在140字以内

#### 处理流程
1. 遍历章节概要中的每个场景
2. 基于场景信息进行动态检索
3. 使用LLM生成详细剧本内容
4. 并行生成对应的场景图描述
5. 验证生成内容的格式和质量

#### 输出格式

**剧本文件 (Script JSON)**
```json
{
  "scene_id": "Chap1_Sc1",
  "dialogue": [
    {
      "speaker": "角色名",
      "text": "对话内容"
    }
  ],
  "narration": [
    "旁白内容"
  ]
}
```

**场景图文件 (Images JSON)**
```json
{
  "scene_id": "Chap1_Sc1",
  "images": [
    {
      "index": 1,
      "description": "环境和动作描述",
      "approx_words": 120
    }
  ]
}
```

### 阶段四：质量验证与输出

#### 目标
确保生成的剧本内容符合纯净性要求，场景图描述准确无误，整体质量达到输出标准。

#### 核心组件

**脚本纯净性验证器 (Script Purity Validator)**
- 检查剧本文件是否仅包含dialogue和narration字段
- 识别并拒绝舞台指令、镜头说明等非法内容
- 使用正则表达式检测括号指令模式

**场景图验证器 (Scene Image Validator)**
- 验证图片数量是否符合字数计算规则
- 检查描述内容是否包含对话或引号
- 确保每张图片字数在限制范围内

**格式标准化器 (Format Standardizer)**
- 统一输出文件格式和命名规范
- 生成双文件结构：script.json + images.json
- 添加必要的元数据信息

#### 验证规则

**脚本纯净性规则**
- 禁止字段：stage_dir, camera, SFX, sound_effect, music, fade, pan
- 禁止模式：`[\[\(].*?(pan|fade|SFX|camera|action).*?[\]\)]`
- 仅允许字段：scene_id, dialogue, narration

**场景图质量规则**
- 图片数量：N = ceil(total_words / 120)
- 单图字数：≤ 140字
- 内容限制：不包含对话、引号或对话关键词

#### 输出结构
```
episode_1/
├── scene_1_script.json    # 纯净对话+旁白
├── scene_1_images.json    # 场景图描述
├── scene_2_script.json
├── scene_2_images.json
└── ...
```

## 关键技术决策

### 检索增强生成 (RAG)
采用检索增强而非大上下文缓存的原因：
- **成本控制**: 每次调用仅使用2-3k tokens相关上下文，而非数百万tokens
- **质量保证**: 避免超长上下文中的信息丢失和召回质量下降
- **稳定性**: 消除大缓存的TTL限制和版本升级风险

### 分层处理策略
先生成概要再逐场景扩展的优势：
- **专注度提升**: LLM可以专注于单个场景的细节生成
- **质量控制**: 概要阶段可以预先发现结构问题
- **并行处理**: 多个场景可以并行扩展，提高效率

### 双文件输出设计
分离剧本和场景图的原因：
- **纯净性保证**: 确保剧本文件100%纯净，仅包含对话和旁白
- **专业分工**: 不同文件服务于不同的后续处理流程
- **质量验证**: 可以独立验证剧本纯净性和场景图质量

## 质量保证机制

### 内容一致性检查
- 角色称呼和行为模式一致性
- 情节发展的逻辑连贯性
- 场景间的自然过渡

### 格式规范验证
- JSON Schema严格验证
- 必填字段完整性检查
- 数据类型和格式约束

### 人工质量检查点
在以下情况触发人工审核：
- 首个场景生成完成后
- 检测到格式或内容异常
- 角色一致性验证失败

## 成本与性能优化

### Token使用优化
- 使用检索增强减少单次调用的Token消耗
- 避免重复传输大量静态上下文
- 预期单章节Token消耗 < 120k

### 处理效率提升
- 场景并行扩展处理
- 向量检索缓存优化
- 批量验证处理

### 错误处理与恢复
- 生成失败时的重试机制
- 格式错误的自动修复
- 优雅降级策略

## 实施路线图

### 第一阶段：核心引擎构建 (2-3周)
1. **语义分块和向量索引系统**
   - 实现自适应语义分块算法
   - 构建向量索引和检索接口
   - 建立全局信息提取器

2. **概要生成和验证组件**
   - 开发结构化概要生成器
   - 实现Schema验证器
   - 建立场景逻辑检查机制

3. **基础场景扩展功能**
   - 实现动态检索引擎
   - 开发场景扩展生成器
   - 建立角色一致性检查

4. **脚本纯净性验证器**
   - 实现非法字段检测
   - 建立正则表达式验证
   - 开发格式标准化器

### 第二阶段：场景图生成 (1-2周)
1. **场景图生成器实现**
   - 开发图片数量计算算法
   - 实现描述生成逻辑
   - 建立内容过滤机制

2. **图片数量计算算法**
   - 实现N = ceil(words/120)算法
   - 建立字数统计准确性验证
   - 优化计算性能

3. **场景图质量验证器**
   - 实现对话内容检测
   - 建立字数限制验证
   - 开发质量评分机制

4. **双文件输出系统**
   - 实现分离式文件生成
   - 建立文件命名规范
   - 开发批量输出功能

### 第三阶段：质量优化 (1-2周)
1. **人工质量检查点集成**
   - 实现质量评分算法
   - 建立人工审核触发机制
   - 开发审核工作流

2. **错误处理和重试机制**
   - 实现智能重试逻辑
   - 建立错误分类系统
   - 开发降级处理策略

3. **性能监控和优化**
   - 建立关键指标监控
   - 实现性能瓶颈检测
   - 优化资源使用效率

4. **端到端测试验证**
   - 开发自动化测试套件
   - 建立质量基准测试
   - 验证系统稳定性

## 成功验收标准

### 功能完整性
- [ ] 支持完整的小说到剧本转换流程
- [ ] 生成符合格式要求的双文件输出
- [ ] 实现脚本纯净性100%保证
- [ ] 支持场景图自动生成和验证

### 质量指标
- [ ] 角色一致性验证通过率 ≥ 95%
- [ ] 场景图数量计算准确率 = 100%
- [ ] 脚本纯净性检测准确率 = 100%
- [ ] 场景逻辑连贯性评分 ≥ 4.0/5.0

### 性能指标
- [ ] 单章节处理时间 ≤ 10分钟
- [ ] Token消耗 < 120k per章节
- [ ] 系统可用性 ≥ 99%
- [ ] 并发处理能力 ≥ 5个场景

### 成本控制
- [ ] 单章节处理成本 ≤ 预算目标
- [ ] Token使用效率提升 ≥ 80%
- [ ] 人工审核工作量 ≤ 20%

## 核心数据流图

```
输入小说JSON
    ↓
[阶段一] 内容预处理与分块
    ├── 语义分块器 → 分块数据
    ├── 向量索引构建器 → 检索索引
    └── 全局信息提取器 → 角色/事件库
    ↓
[阶段二] 章节概要生成
    ├── 概要生成协调器 → 场景概要
    └── 结构化输出验证器 → 验证结果
    ↓
[阶段三] 场景扩展与生成
    ├── 动态检索引擎 → 上下文信息
    ├── 场景扩展生成器 → 剧本内容
    └── 场景图生成器 → 图片描述
    ↓
[阶段四] 质量验证与输出
    ├── 脚本纯净性验证器 → 纯净性报告
    ├── 场景图验证器 → 质量报告
    └── 格式标准化器 → 最终输出
    ↓
双文件输出 (script.json + images.json)
```

## 关键成功因素

### 技术层面
1. **语义分块质量**: 确保分块不破坏语义完整性
2. **检索精准度**: 基于角色和地点的精准检索
3. **生成一致性**: 角色行为和称呼的一致性维护
4. **验证严格性**: 多层验证确保输出质量

### 流程层面
1. **分层处理**: 概要-扩展的分层策略有效性
2. **并行优化**: 场景并行处理的效率提升
3. **错误恢复**: 智能重试和降级机制的可靠性
4. **质量控制**: 人工检查点的合理设置

### 业务层面
1. **成本控制**: Token使用和API调用的经济性
2. **输出质量**: 满足音频剧本制作的专业要求
3. **处理效率**: 满足批量处理的时间要求
4. **可维护性**: 系统的可扩展和可维护特性

## 风险缓解策略

### 技术风险
- **检索质量下降**: 实现混合检索备用方案
- **生成质量不稳定**: 建立多轮验证和修复机制
- **性能瓶颈**: 实现并行处理和缓存优化

### 业务风险
- **成本超预算**: 建立Token使用监控和限制机制
- **质量不达标**: 设置严格的验收标准和测试流程
- **交付延期**: 采用分阶段交付和增量开发

### 运营风险
- **系统故障**: 建立监控告警和快速恢复机制
- **数据安全**: 实现访问控制和数据加密
- **扩展困难**: 采用模块化设计和标准接口

## 结论

本MVP架构专注于音频剧本生成的核心功能，通过分层处理、检索增强和严格验证，确保高质量输出的同时控制成本和复杂度。该设计为后续功能扩展提供了坚实的基础，同时满足当前业务的核心需求。

### 架构优势
- **专注核心**: 避免非必要功能的复杂性
- **质量优先**: 多层验证确保输出质量
- **成本可控**: 通过技术优化实现经济效益
- **可扩展性**: 为未来功能扩展预留空间

### 预期收益
- **效率提升**: 自动化处理大幅提高生产效率
- **质量保证**: 结构化验证确保输出质量稳定
- **成本优化**: 智能检索和并行处理降低运营成本
- **标准化**: 统一的输出格式便于后续处理

该MVP设计为音频剧本自动化生成建立了一个可靠、高效、可维护的技术基础，为规模化内容生产提供了强有力的技术支撑。
